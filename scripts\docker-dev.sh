#!/bin/bash

# Docker Development Management Script
# Usage: ./scripts/docker-dev.sh [start|stop|restart|logs|shell|db|clean]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to start development environment
start_dev() {
    print_status "Starting development environment..."
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before starting."
        return 1
    fi
    
    docker-compose up -d
    print_success "Development environment started!"
    print_status "Services:"
    print_status "  - API Server: http://localhost:8080"
    print_status "  - Database: localhost:5432"
    print_status "  - pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
    print_status ""
    print_status "To view logs: $0 logs"
    print_status "To access shell: $0 shell"
}

# Function to start with tools (pgAdmin)
start_with_tools() {
    print_status "Starting development environment with tools..."
    docker-compose --profile tools up -d
    print_success "Development environment with tools started!"
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping development environment..."
    docker-compose down
    print_success "Development environment stopped!"
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting development environment..."
    docker-compose restart
    print_success "Development environment restarted!"
}

# Function to view logs
view_logs() {
    print_status "Viewing application logs (Ctrl+C to exit)..."
    docker-compose logs -f app
}

# Function to access application shell
access_shell() {
    print_status "Accessing application container shell..."
    docker-compose exec app sh
}

# Function to access database shell
access_db() {
    print_status "Accessing PostgreSQL shell..."
    docker-compose exec postgres psql -U carwash_user -d mobile_carwash_dev
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    docker-compose exec app npm run migrate
    print_success "Database migrations completed!"
}

# Function to seed database
seed_db() {
    print_status "Seeding database..."
    docker-compose exec app npm run seed
    print_success "Database seeded!"
}

# Function to backup database
backup_db() {
    print_status "Creating database backup..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_file="backup_${timestamp}.sql"
    docker-compose exec postgres pg_dump -U carwash_user mobile_carwash_dev > "backups/${backup_file}"
    print_success "Database backup created: backups/${backup_file}"
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down -v
    docker system prune -f
    docker volume prune -f
    print_success "Cleanup completed!"
}

# Function to show status
show_status() {
    print_status "Docker Compose Status:"
    docker-compose ps
    print_status ""
    print_status "Docker Images:"
    docker images | grep mobile-carwash || echo "No mobile-carwash images found"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       Start development environment"
    echo "  tools       Start with development tools (pgAdmin)"
    echo "  stop        Stop development environment"
    echo "  restart     Restart development environment"
    echo "  logs        View application logs"
    echo "  shell       Access application container shell"
    echo "  db          Access PostgreSQL shell"
    echo "  migrate     Run database migrations"
    echo "  seed        Seed database with initial data"
    echo "  backup      Create database backup"
    echo "  status      Show Docker status"
    echo "  clean       Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs"
    echo "  $0 shell"
}

# Create backups directory if it doesn't exist
mkdir -p backups

# Main script logic
case "${1:-start}" in
    "start")
        start_dev
        ;;
    "tools")
        start_with_tools
        ;;
    "stop")
        stop_dev
        ;;
    "restart")
        restart_dev
        ;;
    "logs")
        view_logs
        ;;
    "shell")
        access_shell
        ;;
    "db")
        access_db
        ;;
    "migrate")
        run_migrations
        ;;
    "seed")
        seed_db
        ;;
    "backup")
        backup_db
        ;;
    "status")
        show_status
        ;;
    "clean")
        cleanup
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Invalid command: $1"
        show_usage
        exit 1
        ;;
esac
