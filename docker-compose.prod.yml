# Production Docker Compose Configuration
version: '3.8'

services:
  # Mobile Carwash API Server - Production
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: mobile-carwash-api-prod
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      # Use external production database URL
      - DB_URI=${DB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-24h}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    # No volume mounts in production for security
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - carwash-network
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL Database for Production
  postgres:
    image: postgres:15-alpine
    container_name: mobile-carwash-postgres-prod
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-mobile_carwash_prod}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    # Don't expose ports in production for security
    # ports:
    #   - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - carwash-network
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB:-mobile_carwash_prod}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for Session Storage - Production
  redis:
    image: redis:7-alpine
    container_name: mobile-carwash-redis-prod
    # Don't expose ports in production
    # ports:
    #   - "6379:6379"
    volumes:
      - redis_prod_data:/data
    networks:
      - carwash-network
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: mobile-carwash-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - carwash-network
    restart: always
    profiles:
      - proxy
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local

networks:
  carwash-network:
    driver: bridge
    name: mobile-carwash-network-prod
